{"id": "evidence-duckdb-workflow", "name": "Evidence DuckDB Workflow", "version": "1.0.0", "description": "Evidence framework with DuckDB integration for Domo dataset analysis and interactive reporting", "author": "Evidence Team", "supportEmail": "<EMAIL>", "tags": ["analytics", "sql", "duckdb", "evidence", "workflow", "reporting", "data-visualization"], "icon": "icon.svg", "screenshots": ["screenshot1.png", "screenshot2.png"], "sizing": {"width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "maxWidth": 1920, "maxHeight": 1080}, "entryPoint": "index.html", "permissions": ["data:read", "data:write", "user:read", "embed"], "configuration": {"properties": {"defaultTablePrefix": {"type": "string", "title": "Default Table Prefix", "description": "Prefix to add to all loaded table names", "default": "domo_"}, "maxRowsPerLoad": {"type": "number", "title": "Maximum Rows Per Load", "description": "Maximum number of rows to load in a single operation", "default": 100000, "minimum": 1000, "maximum": 1000000}, "enableAutoRefresh": {"type": "boolean", "title": "Enable Auto Refresh", "description": "Automatically refresh data when datasets are updated", "default": true}, "refreshInterval": {"type": "number", "title": "Refresh <PERSON> (minutes)", "description": "How often to check for data updates", "default": 60, "minimum": 5, "maximum": 1440}, "duckdbMemoryLimit": {"type": "string", "title": "DuckDB Memory Limit", "description": "Memory limit for DuckDB operations (e.g., '1GB', '512MB')", "default": "512MB", "enum": ["256MB", "512MB", "1GB", "2GB"]}, "enableQueryCache": {"type": "boolean", "title": "Enable Query Caching", "description": "Cache query results to improve performance", "default": true}, "defaultDateFormat": {"type": "string", "title": "Default Date Format", "description": "Default format for displaying dates", "default": "YYYY-MM-DD", "enum": ["YYYY-MM-DD", "MM/DD/YYYY", "DD/MM/YYYY", "YYYY-MM-DD HH:mm:ss"]}}}, "dataConnections": {"input": {"type": "dataset", "required": false, "multiple": true, "description": "Optional: Pre-select datasets to load"}}, "mapping": {"datasets": [{"alias": "primary_dataset", "label": "Primary Dataset", "description": "Main dataset for analysis", "required": false, "fields": []}], "options": {"allowMultipleDatasets": true, "dynamicFieldMapping": true, "preserveFieldTypes": true}}, "features": {"responsive": true, "fullscreen": true, "export": true, "print": false, "share": true}, "dependencies": {"domo": ">=2.0.0"}, "build": {"webpack": {"entry": "index.html", "output": "dist/"}}, "deployment": {"type": "static", "files": ["index.html", "app.js", "styles.css", "domo-manifest.json", "*.ico", "*.png", "*.svg"]}, "runtime": {"type": "browser", "supportedBrowsers": ["chrome", "firefox", "safari", "edge"], "minBrowserVersions": {"chrome": "90", "firefox": "88", "safari": "14", "edge": "90"}}, "security": {"contentSecurityPolicy": {"scriptSrc": ["'self'", "'unsafe-inline'", "'unsafe-eval'"], "styleSrc": ["'self'", "'unsafe-inline'"], "imgSrc": ["'self'", "data:", "blob:"], "connectSrc": ["'self'"]}}}