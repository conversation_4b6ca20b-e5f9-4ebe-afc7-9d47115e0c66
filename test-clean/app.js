// Simple test app for Domo DDX
console.log("Test app loaded");

// Test basic Domo functionality
if (typeof domo !== 'undefined') {
    console.log("Domo object is available");

    // Try to get some basic data
    domo.get('/data/v2/excel?limit=10')
        .then(function(data) {
            console.log("Excel data:", data);
        })
        .catch(function(error) {
            console.error("Error fetching excel data:", error);
        });

    domo.get('/data/v2/gov?limit=10')
        .then(function(data) {
            console.log("Gov data:", data);
        })
        .catch(function(error) {
            console.error("Error fetching gov data:", error);
        });
} else {
    console.log("Domo object not available - running in development mode");
}