{"id": "evidence-duckdb-workflow", "name": "Evidence DuckDB Workflow", "version": "1.0.0", "description": "Evidence framework with DuckDB integration for Domo dataset analysis", "author": "Evidence Team", "supportEmail": "<EMAIL>", "tags": ["analytics", "sql", "duckdb", "evidence", "workflow"], "icon": "icon.svg", "screenshots": ["screenshot1.png", "screenshot2.png"], "sizing": {"width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}, "entryPoint": "index.html", "permissions": ["data:read", "data:write", "user:read"], "configuration": {"properties": {"defaultTablePrefix": {"type": "string", "title": "Default Table Prefix", "description": "Prefix to add to all loaded table names", "default": "domo_"}, "maxRowsPerLoad": {"type": "number", "title": "Maximum Rows Per Load", "description": "Maximum number of rows to load in a single operation", "default": 100000, "minimum": 1000, "maximum": 1000000}, "enableAutoRefresh": {"type": "boolean", "title": "Enable Auto Refresh", "description": "Automatically refresh data when datasets are updated", "default": true}, "refreshInterval": {"type": "number", "title": "Refresh <PERSON> (minutes)", "description": "How often to check for data updates", "default": 60, "minimum": 5, "maximum": 1440}}}, "dataConnections": {"input": {"type": "dataset", "required": false, "multiple": true, "description": "Optional: Pre-select datasets to load"}}, "mapping": {"datasets": [{"alias": "primary_dataset", "label": "Primary Dataset", "description": "Main dataset for analysis", "required": false, "fields": []}], "options": {"allowMultipleDatasets": true, "dynamicFieldMapping": true, "preserveFieldTypes": true}}, "features": {"responsive": true, "fullscreen": true, "export": true, "print": false, "share": true}, "dependencies": {"domo": ">=2.0.0"}}