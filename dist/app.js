// Some DataSets are massive and will bring any web browser to its knees if you
// try to load the entire thing. To keep your app performing optimally, take
// advantage of filtering, aggregations, and group by's to bring down just the
// data your app needs. Do not include all columns in your data mapping file,
// just the ones you need.
//
// For additional documentation on how you can query your data, please refer to
// https://developer.domo.com/docs/dev-studio/dev-studio-data
domo.get('/data/v2/excel?limit=100')
    .then(function(excel){
      console.log("excel", excel);
    });

domo.get('/data/v2/gov?limit=100')
    .then(function(gov){
      console.log("gov", gov);
    });

