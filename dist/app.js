// Evidence DuckDB Workflow - Domo App
console.log("Evidence DuckDB Workflow app loaded");

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM ready, initializing app...");

    // Update status display
    const statusDisplay = document.getElementById('status-display');
    const excelResults = document.getElementById('excel-results');
    const govResults = document.getElementById('gov-results');

    // Check if Domo is available
    if (typeof domo !== 'undefined') {
        console.log("Domo object is available");
        statusDisplay.innerHTML = '<p style="color: green;">✅ Connected to Domo DDX</p>';

        // Try to get Excel data
        domo.get('/data/v2/excel?limit=5')
            .then(function(data) {
                console.log("Excel data:", data);
                excelResults.innerHTML = `
                    <p style="color: green;">✅ Data loaded successfully</p>
                    <p>Records: ${data.length}</p>
                    <pre>${JSON.stringify(data.slice(0, 2), null, 2)}</pre>
                `;
            })
            .catch(function(error) {
                console.error("Error fetching excel data:", error);
                excelResults.innerHTML = `<p style="color: red;">❌ Error: ${error.message}</p>`;
            });

        // Try to get Government data
        domo.get('/data/v2/gov?limit=5')
            .then(function(data) {
                console.log("Gov data:", data);
                govResults.innerHTML = `
                    <p style="color: green;">✅ Data loaded successfully</p>
                    <p>Records: ${data.length}</p>
                    <pre>${JSON.stringify(data.slice(0, 2), null, 2)}</pre>
                `;
            })
            .catch(function(error) {
                console.error("Error fetching gov data:", error);
                govResults.innerHTML = `<p style="color: red;">❌ Error: ${error.message}</p>`;
            });

    } else {
        console.log("Domo object not available - running in development mode");
        statusDisplay.innerHTML = '<p style="color: orange;">⚠️ Development mode - Domo SDK not available</p>';
        excelResults.innerHTML = '<p style="color: gray;">📊 Mock data would be shown here</p>';
        govResults.innerHTML = '<p style="color: gray;">📊 Mock data would be shown here</p>';
    }
});

